[{"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\lib\\sanity.js": "1", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js": "2", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js": "3", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js": "4", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js": "5", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js": "6", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js": "7", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js": "8", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js": "9", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js": "10", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js": "11", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js": "13", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js": "14", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js": "15", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js": "16", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js": "17", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js": "18", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\EnterpriseNavbar.tsx": "22", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx": "23", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\EnterprisePerformanceDashboard.tsx": "24", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\EnterprisePWAInstaller.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js": "26", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx": "27", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js": "28", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js": "30", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js": "31", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js": "32", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js": "33", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js": "34", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js": "35", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js": "36", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js": "37", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js": "38", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js": "39", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js": "40", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js": "41", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js": "42", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js": "43"}, {"size": 6440, "mtime": 1750136629000, "results": "44", "hashOfConfig": "45"}, {"size": 4750, "mtime": 1750228680000, "results": "46", "hashOfConfig": "45"}, {"size": 6263, "mtime": 1750228714000, "results": "47", "hashOfConfig": "45"}, {"size": 4150, "mtime": 1750227113000, "results": "48", "hashOfConfig": "45"}, {"size": 2352, "mtime": 1750227130000, "results": "49", "hashOfConfig": "45"}, {"size": 7630, "mtime": 1750227164000, "results": "50", "hashOfConfig": "45"}, {"size": 2591, "mtime": 1750223066000, "results": "51", "hashOfConfig": "45"}, {"size": 1555, "mtime": 1750137055000, "results": "52", "hashOfConfig": "45"}, {"size": 558, "mtime": 1750137055000, "results": "53", "hashOfConfig": "45"}, {"size": 755, "mtime": 1750137055000, "results": "54", "hashOfConfig": "45"}, {"size": 4688, "mtime": 1752410773610, "results": "55", "hashOfConfig": "45"}, {"size": 996, "mtime": 1752789347014, "results": "56", "hashOfConfig": "57"}, {"size": 3811, "mtime": 1752534114385, "results": "58", "hashOfConfig": "45"}, {"size": 2920, "mtime": 1750137055000, "results": "59", "hashOfConfig": "45"}, {"size": 2000, "mtime": 1752534592254, "results": "60", "hashOfConfig": "45"}, {"size": 2160, "mtime": 1752661449491, "results": "61", "hashOfConfig": "45"}, {"size": 6117, "mtime": 1752826937739, "results": "62", "hashOfConfig": "45"}, {"size": 1306, "mtime": 1750137055000, "results": "63", "hashOfConfig": "45"}, {"size": 5482, "mtime": 1752761132661, "results": "64", "hashOfConfig": "57"}, {"size": 11107, "mtime": 1752584749354, "results": "65", "hashOfConfig": "57"}, {"size": 9367, "mtime": 1752584749214, "results": "66", "hashOfConfig": "57"}, {"size": 19030, "mtime": 1752828902760, "results": "67", "hashOfConfig": "57"}, {"size": 6089, "mtime": 1752584748691, "results": "68", "hashOfConfig": "57"}, {"size": 47426, "mtime": 1752995137877, "results": "69", "hashOfConfig": "57"}, {"size": 33853, "mtime": 1752995154408, "results": "70", "hashOfConfig": "57"}, {"size": 13396, "mtime": 1752509953353, "results": "71", "hashOfConfig": "45"}, {"size": 8239, "mtime": 1752584747158, "results": "72", "hashOfConfig": "57"}, {"size": 1853, "mtime": 1752846711139, "results": "73", "hashOfConfig": "45"}, {"size": 1189, "mtime": 1752229991826, "results": "74", "hashOfConfig": "45"}, {"size": 17127, "mtime": 1750189264000, "results": "75", "hashOfConfig": "45"}, {"size": 1147, "mtime": 1750137055000, "results": "76", "hashOfConfig": "45"}, {"size": 1901, "mtime": 1750137056000, "results": "77", "hashOfConfig": "45"}, {"size": 1700, "mtime": 1752558963287, "results": "78", "hashOfConfig": "45"}, {"size": 11592, "mtime": 1752669072282, "results": "79", "hashOfConfig": "45"}, {"size": 18399, "mtime": 1752417314631, "results": "80", "hashOfConfig": "45"}, {"size": 11007, "mtime": 1752789380820, "results": "81", "hashOfConfig": "45"}, {"size": 2033, "mtime": 1750137056000, "results": "82", "hashOfConfig": "45"}, {"size": 15647, "mtime": 1752532937438, "results": "83", "hashOfConfig": "45"}, {"size": 19639, "mtime": 1752789179363, "results": "84", "hashOfConfig": "45"}, {"size": 10182, "mtime": 1750224902000, "results": "85", "hashOfConfig": "45"}, {"size": 2224, "mtime": 1752757322917, "results": "86", "hashOfConfig": "45"}, {"size": 4983, "mtime": 1752413498649, "results": "87", "hashOfConfig": "45"}, {"size": 1951, "mtime": 1752826470161, "results": "88", "hashOfConfig": "45"}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lcw2q5", {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19x6zoz", {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\lib\\sanity.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\EnterpriseNavbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\EnterprisePerformanceDashboard.tsx", ["218", "219"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\EnterprisePWAInstaller.tsx", ["220", "221"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js", [], [], {"ruleId": "222", "severity": 1, "message": "223", "line": 576, "column": 9, "nodeType": "224", "endLine": 593, "endColumn": 4}, {"ruleId": "222", "severity": 1, "message": "225", "line": 605, "column": 9, "nodeType": "224", "endLine": 608, "endColumn": 4}, {"ruleId": "222", "severity": 1, "message": "226", "line": 257, "column": 9, "nodeType": "224", "endLine": 297, "endColumn": 4}, {"ruleId": "227", "severity": 1, "message": "228", "line": 713, "column": 31, "nodeType": "229", "endLine": 717, "endColumn": 33}, "react-hooks/exhaustive-deps", "The 'initializeWebSocket' function makes the dependencies of useEffect Hook (at line 574) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'initializeWebSocket' in its own useCallback() Hook.", "VariableDeclarator", "The 'startDataCollection' function makes the dependencies of useEffect Hook (at line 574) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'startDataCollection' in its own useCallback() Hook.", "The 'initializePWAInstaller' function makes the dependencies of useEffect Hook (at line 255) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'initializePWAInstaller' in its own useCallback() Hook.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]